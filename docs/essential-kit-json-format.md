# Essential Kit JSON Format Documentation

This document describes the complete JSON format for configuring Essential Kit data in the EverTrek system.

## Overview

Essential Kit data is stored as JSON in Copy Items with the category `essential-kit`. The JSON structure supports equipment items, free equipment hire configuration, and various display options.

## Top-Level Structure

```json
{
  "products": [
    // Array of equipment items
  ],
  "free_equipment_hire": true | false | {
    // Free equipment hire configuration object
  }
}
```

## Equipment Items (`products` array)

Each equipment item in the `products` array supports the following properties:

### Required Properties

| Property | Type | Description |
|----------|------|-------------|
| `item_name` | `string` | Display name of the equipment item |
| `product_name` | `string` | Full product name (used for SEO/metadata) |
| `item_description` | `string` | HTML description of the item |
| `image` | `string` | Path to the equipment image |

### Optional Properties

| Property | Type | Description |
|----------|------|-------------|
| `url` | `string` | External shop URL - makes the entire item clickable |
| `rent_locally` | `boolean` | Shows "Rent Locally" badge if `true` |
| `free_hire` | `boolean` | Shows "FREE Hire" badge if `true` |
| `rent_locally_popover` | `string` | Custom tooltip text for rent locally badge |
| `free_hire_popover` | `string` | Custom tooltip text for free hire badge |

### Equipment Item Example

```json
{
  "item_name": "Waterproof jacket",
  "product_name": "Latok Mountain GORE-TEX® Pro Jacket",
  "item_description": "A versatile gore-tex or similar material jacket is ideal for year round walking, hiking and trekking in the roughest weather. Jackets like the Latok Mountain GORE-TEX® Pro Jacket offer great weather protection and breathability when you need it.",
  "image": "/templates/zenbase/images/essential-kit/01-waterproof-jacket.jpg",
  "url": "https://evertrek.com/products/latok_mountain_gore_tex_pro_jacket",
  "rent_locally": true,
  "free_hire": true,
  "rent_locally_popover": "Available for rent at local outdoor stores in your destination",
  "free_hire_popover": "This item is included free with your EverTrek booking"
}
```

## Free Equipment Hire Configuration

The `free_equipment_hire` property controls the display of the FREE Equipment Hire tile that appears as the second item in the Essential Kit grid.

### Legacy Boolean Format (Backward Compatible)

```json
{
  "free_equipment_hire": true  // Shows tile with default content
}
```

```json
{
  "free_equipment_hire": false  // Hides the tile completely
}
```

### New Object Format (Recommended)

```json
{
  "free_equipment_hire": {
    "show": true,
    "title": "FREE equipment hire!",
    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
    "show_footer": true,
    "footer_text": "Check out our partner discounts for extra kit.",
    "url": "https://evertrek.com/partner-discounts"
  }
}
```

### Free Equipment Hire Properties

| Property | Type | Required | Default | Description |
|----------|------|----------|---------|-------------|
| `show` | `boolean` | No | `true` | Whether to display the FREE Equipment Hire tile |
| `title` | `string` | No | `"FREE equipment hire!"` | Title/heading for the tile |
| `content` | `string` | No | Default content | HTML content for the tile body |
| `show_footer` | `boolean` | No | `false` | Whether to show the partner discounts footer |
| `footer_text` | `string` | No | `"Check out our partner discounts for extra kit."` | Text for the footer |
| `url` | `string` | No | `"#"` | URL for the partner discounts button. **Note**: Footer/button only displays if `show_footer` is `true` AND a valid URL is provided (not empty or "#"). External URLs open in new tab. |

### Default Content

When using the legacy boolean format or omitting properties in the object format, the following defaults are used:

- **Title**: `"FREE equipment hire!"`
- **Content**: HTML describing free winter sleeping bags, down jacket, and additional items
- **Footer**: Hidden by default (`show_footer: false`)
- **Footer Text**: `"Check out our partner discounts for extra kit."`

## Complete Example

```json
{
  "products": [
    {
      "item_name": "Waterproof jacket",
      "product_name": "Latok Mountain GORE-TEX® Pro Jacket",
      "item_description": "A versatile gore-tex or similar material jacket is ideal for year round walking, hiking and trekking in the roughest weather.",
      "image": "/templates/zenbase/images/essential-kit/01-waterproof-jacket.jpg",
      "url": "https://evertrek.com/products/latok_mountain_gore_tex_pro_jacket",
      "rent_locally": true,
      "free_hire": true
    },
    {
      "item_name": "Insulated jacket", 
      "product_name": "Montane Anti-Freeze XT Down Hoodie",
      "item_description": "A down or synthetic insulated jacket is a must for sub-zero conditions.",
      "image": "/templates/zenbase/images/essential-kit/02-insulated-jacket.jpg",
      "url": "https://evertrek.com/products/montane_anti_freeze_xt_down_hoodie"
    },
    {
      "item_name": "Sleeping bag",
      "product_name": "Rab Ascent 700",
      "item_description": "A warm and reliable down-filled sleeping bag is essential for multi-day trekking.",
      "image": "/templates/zenbase/images/essential-kit/03-sleeping-bag.jpg",
      "url": "https://evertrek.com/products/rab_ascent_700",
      "free_hire": true,
      "free_hire_popover": "Free sleeping bag hire available - contact us when booking"
    }
  ],
  "free_equipment_hire": {
    "show": true,
    "title": "FREE equipment hire!",
    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
    "show_footer": true,
    "footer_text": "Check out our partner discounts for extra kit.",
    "url": "https://evertrek.com/partner-discounts"
  }
}
```

## Validation Rules

### Equipment Items
- `item_name`: Required, non-empty string
- `product_name`: Required, non-empty string  
- `item_description`: Required, non-empty string (HTML allowed)
- `image`: Required, valid path starting with `/` or `http`
- `url`: Optional, must be valid URL if provided
- Badge properties (`rent_locally`, `free_hire`): Optional boolean values
- Popover properties: Optional strings, used only when corresponding badge is `true`

### Free Equipment Hire
- Legacy format: Must be boolean `true` or `false`
- Object format: All properties optional, merged with defaults
- `title`: String, used as-is (HTML entities will be escaped)
- `content`: String, rendered as HTML (no escaping)
- `show_footer`: Boolean
- `footer_text`: String, HTML entities will be escaped
- `url`: String, must be valid URL or `#` for placeholder (external URLs automatically open in new tab)

## Migration Guide

### From Legacy Format
If you're currently using:
```json
{
  "free_equipment_hire": true
}
```

This will continue to work with default styling and content.

### To New Format
To customize the FREE Equipment Hire tile:
```json
{
  "free_equipment_hire": {
    "show": true,
    "title": "Custom Title",
    "content": "&lt;p&gt;Custom HTML content here&lt;/p&gt;",
    "show_footer": true,
    "footer_text": "Custom footer message",
    "url": "https://example.com/partner-discounts"
  }
}
```

## Technical Notes

- The FREE Equipment Hire tile appears as the **second item** in the grid (after the first equipment item)
- HTML in `content` is rendered directly (no escaping)
- HTML in `title` and `footer_text` is escaped for security
- Badge popovers use the `title` attribute for accessibility
- External links (`url` properties) open in new tabs with `rel="noopener"`
- URLs starting with `http` automatically get `target="_blank"` and `rel="noopener"` attributes
- Internal links and `#` placeholders open in the same tab
- Images should be optimized for web display (recommended: WebP format, max 800px width)

### WYSIWYG Editor Handling

When entering JSON in the Joomla WYSIWYG editor, HTML tags within JSON strings must be escaped to prevent the editor from treating them as actual HTML elements.

**The Problem**: WYSIWYG editors see HTML tags inside JSON strings as real HTML and try to "fix" invalid nesting, which breaks the JSON.

**The Solution**: Escape HTML tags in JSON content using HTML entities.

### Required HTML Escaping for WYSIWYG Editor

When entering JSON with HTML content, you must escape the HTML tags:

- `<` becomes `&lt;`
- `>` becomes `&gt;`
- Use single quotes (`'`) for HTML attributes instead of double quotes to avoid JSON conflicts

### Example for WYSIWYG Editor

**✅ CORRECT - Escaped HTML in JSON:**
```json
{
  "products": [...],
  "free_equipment_hire": {
    "show": true,
    "title": "FREE equipment hire!",
    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt;.&lt;/p&gt;",
    "show_footer": true,
    "footer_text": "Check out our partner discounts for extra kit.",
    "url": "https://evertrek.com/partner-discounts"
  }
}
```

**❌ INCORRECT - Unescaped HTML in JSON:**
```json
{
  "content": "<p>This will be broken by the WYSIWYG editor</p>"
}
```

### Processing Flow

1. **Content Entry**: HTML tags are escaped in JSON (`&lt;p&gt;` instead of `<p>`)
2. **WYSIWYG Storage**: Editor treats escaped tags as text, not HTML elements
3. **Template Processing**: System automatically unescapes HTML entities back to proper HTML
4. **Final Rendering**: HTML displays correctly in the browser

### Quick Escaping Reference

| Original | Escaped | Usage |
|----------|---------|-------|
| `<p>` | `&lt;p&gt;` | Paragraph tags |
| `<span class="highlight">` | `&lt;span class='highlight'&gt;` | Spans with classes (use single quotes) |
| `<strong>` | `&lt;strong&gt;` | Bold text |
| `<br>` | `&lt;br&gt;` | Line breaks |

## CSS Classes

The following CSS classes are used for styling:

### FREE Equipment Hire Tile
- `.free-equipment-hire-tile` - Main container
- `.free-equipment-hire-header` - Title styling
- `.free-equipment-hire-content` - Body content container
- `.free-equipment-hire-highlight` - Highlighted text spans
- `.free-equipment-hire-footer` - Footer container
- `.free-equipment-hire-footer-text` - Footer text
- `.partner-discounts-btn` - Partner discounts button

### Equipment Items
- `.essential-kit-grid` - Grid container
- `.essential-kit-grid-item` - Individual item container
- `.essential-kit-image-container` - Image wrapper
- `.essential-kit-image` - Equipment image
- `.essential-kit-pills` - Badge container
- `.essential-kit-pill` - Individual badge
- `.essential-kit-content` - Text content container
- `.essential-kit-title` - Item name
- `.essential-kit-description` - Item description
- `.essential-kit-shop-indicator` - Shop button
