<?php
defined('_JEXEC') or die;

// Create helper instance
$helper = new ZenbaseCustomHelpers();

// Get essential kit data
$essentialKit = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'essential-kit');
?>

<button class="d-md-block d-lg-none js-mobile-tabs collapsed" data-bs-toggle="collapse" data-bs-target="#<?= $helper->toggleData($tab05); ?>-tab-content" aria-expanded="false" aria-controls="<?= $helper->toggleData($tab05); ?>-tab-content">
  <span><?php echo $tab05_display; ?></span>
</button>

<div class="<?php echo $contentClass; ?> essential-kit-container" id="<?= $helper->toggleData($tab05); ?>-tab-content" role="tabpanel" aria-labelledby="<?= $helper->toggleData($tab05); ?>-tab">
	<!------------------------------------------------------------------------------
  // Tab/Accordion Content - Essential Kit
  //----------------------------------------------------------------------------->
	<div class="zen-holiday__content-box">
		<div class="zen-holiday__content-item">
			<div class="container">
				<div class="d-flex justify-content-between align-items-start mb-4">
					<div>
						<h3 class="section-title">Essential Kit</h3>
					</div>
				</div>

				<div class="d-flex justify-content-between align-items-center mb-4">
					<p class="intro mb-0">Explore our essential kit picks so you’re comfortable, safe, and ready for every step. <a style="color:#FE7720" data-bs-toggle="modal" data-bs-target="#packingListModal">Download</a> the full checklist to nail your packing. </p>

					<div class="essential-kit-header-button">
						<?php
						$packingList = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'packing-list');
						if ($packingList && isset($packingList['packing-list']) && isset($packingList['packing-list']->items)):
							$content = preg_replace('/^<p[^>]*>(.*)<\/p>$/s', '$1', $packingList['packing-list']->items[0]->content);
							$formData = json_decode($content, true);
							if ($formData && isset($formData['form_url'], $formData['iframe_id'], $formData['form_id'], $formData['form_name'], $formData['form_title'])):
						?>
						<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#packingListModal" style="white-space: nowrap;">
							Download full packing list
							<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
								<path d="M9.00002 7.21051C8.61002 7.60051 8.61002 8.23051 9.00002 8.62051L12.88 12.5005L9.00002 16.3805C8.61002 16.7705 8.61002 17.4005 9.00002 17.7905C9.39002 18.1805 10.02 18.1805 10.41 17.7905L15 13.2005C15.39 12.8105 15.39 12.1805 15 11.7905L10.41 7.20051C10.03 6.82051 9.39002 6.82051 9.00002 7.21051Z" fill="currentColor"/>
							</svg>
						</button>
						<?php
							endif;
						endif;
						?>
					</div>
				</div>

				<?php if ($essentialKit && isset($essentialKit['essential-kit']) && isset($essentialKit['essential-kit']->items)): ?>
					<?php foreach ($essentialKit['essential-kit']->items as $item): ?>
						<?php
							// Strip wrapping <p> tags from content before JSON decode
							$content = preg_replace('/^<p[^>]*>(.*)<\/p>$/s', '$1', $item->content);

							// Try to decode HTML entities in the entire content first
							$decodedContent = html_entity_decode($content, ENT_QUOTES | ENT_HTML5, 'UTF-8');

							// Try parsing the decoded content first
							$kitData = json_decode($decodedContent, true);
							$jsonError = json_last_error();

							// If that fails, try the original content
							if ($jsonError !== JSON_ERROR_NONE) {
								$kitData = json_decode($content, true);
								$jsonError = json_last_error();
							}

							$jsonErrorMsg = json_last_error_msg();
							$kitItems = isset($kitData['products']) ? $kitData['products'] : null;

							// Process free equipment hire configuration
							$freeEquipmentHire = false;
							$freeHireConfig = [
								'show' => false,
								'title' => 'FREE equipment hire!',
								'content' => '<p>All EverTrekkers receive <span class="free-equipment-hire-highlight">FREE winter sleeping bags</span> and a <span class="free-equipment-hire-highlight">FREE £110 down jacket</span> – just let us know when booking to reserve.</p><p><strong>PLUS:</strong> Get a <span class="free-equipment-hire-highlight">FREE 80 Ltr duffel bag, t-shirt, cap, and map</span> when you arrive in Kathmandu!</p>',
								'show_footer' => false,
								'footer_text' => 'Check out our partner discounts for extra kit.',
								'url' => '#'
							];

							if (isset($kitData['free_equipment_hire'])) {
								if ($kitData['free_equipment_hire'] === true || $kitData['free_equipment_hire'] === 'true') {
									// Legacy boolean format - show with defaults
									$freeEquipmentHire = true;
									$freeHireConfig['show'] = true;
								} elseif ($kitData['free_equipment_hire'] === false || $kitData['free_equipment_hire'] === 'false') {
									// Explicitly hidden
									$freeEquipmentHire = false;
									$freeHireConfig['show'] = false;
								} elseif (is_array($kitData['free_equipment_hire'])) {
									// New object format - merge with defaults
									$freeEquipmentHire = true;
									$freeHireConfig['show'] = true;
									$freeHireConfig = array_merge($freeHireConfig, $kitData['free_equipment_hire']);

									// HTML content is already decoded at the JSON level, no need to decode again
								}
							}
						?>

						<div class="row gx-0">
							<div class="col-12">
								<?php if ($kitItems === null): ?>
									<div class="zen-alert zen-alert--warning">
										<p>Sorry, we couldn't load the essential kit items at this time. Please try refreshing the page.</p>
									</div>
									<!-- Essential Kit JSON Parse Error Details:
									JSON Error: <?php echo htmlspecialchars($jsonErrorMsg); ?> (Code: <?php echo $jsonError; ?>)
									Content Length: <?php echo strlen($content); ?> characters
									Raw Content: <?php echo htmlspecialchars(substr($content, 0, 500)); ?><?php echo strlen($content) > 500 ? '...' : ''; ?>
									Decoded Content: <?php echo htmlspecialchars(substr($decodedContent, 0, 500)); ?><?php echo strlen($decodedContent) > 500 ? '...' : ''; ?>
									-->
								<?php elseif (is_array($kitItems)): ?>
									<div class="essential-kit-grid">
										<?php
										$itemIndex = 0;
										foreach ($kitItems as $kit):
											$itemIndex++;

											// Insert FREE Equipment Hire Message Tile as second item (only if free_equipment_hire is configured to show)
											if ($itemIndex == 2 && $freeHireConfig['show']): ?>
												<!-- FREE Equipment Hire Message Tile -->
												<div class="free-equipment-hire-tile">
													<h3 class="free-equipment-hire-header"><?php echo htmlspecialchars($freeHireConfig['title']); ?></h3>
													<div class="free-equipment-hire-content">
														<?php echo $freeHireConfig['content']; ?>
													</div>
													<?php if ($freeHireConfig['show_footer'] && !empty($freeHireConfig['url']) && $freeHireConfig['url'] !== '#'): ?>
													<div class="free-equipment-hire-footer">
														<p class="free-equipment-hire-footer-text"><?php echo htmlspecialchars($freeHireConfig['footer_text']); ?></p>
														<a href="<?php echo htmlspecialchars($freeHireConfig['url']); ?>" class="partner-discounts-btn" <?php echo (strpos($freeHireConfig['url'], 'http') === 0) ? 'target="_blank" rel="noopener"' : ''; ?>>
															Partner Discounts
															<img src="/templates/zenbase/icons/trips/open_in_new.svg" width="16" height="16" alt="">
														</a>
													</div>
													<?php endif; ?>
												</div>
											<?php endif;

											// Check for badge properties in JSON data
											$showRentPill = isset($kit['rent_locally']) && $kit['rent_locally'] === true;
											$showFreePill = isset($kit['free_hire']) && $kit['free_hire'] === true;

											// Get popover text (with defaults)
											$rentPopoverText = isset($kit['rent_locally_popover']) ? $kit['rent_locally_popover'] : 'Available for rent at local outdoor stores in your destination';
											$freePopoverText = isset($kit['free_hire_popover']) ? $kit['free_hire_popover'] : 'This item is included free with your EverTrek booking';

											// Check if shop button should be shown
											$showShopButton = !empty($kit['url']);
										?>
											<?php if ($showShopButton): ?>
											<a href="<?php echo htmlspecialchars($kit['url']); ?>" class="essential-kit-grid-item" target="_blank" rel="noopener">
											<?php else: ?>
											<div class="essential-kit-grid-item">
											<?php endif; ?>
												<?php if (!empty($kit['image'])): ?>
													<div class="essential-kit-image-container">
														<img src="<?php echo htmlspecialchars($kit['image']); ?>" alt="<?php echo htmlspecialchars($kit['item_name'] ?? ''); ?>" class="essential-kit-image">
														<?php if ($showRentPill || $showFreePill): ?>
														<div class="essential-kit-pills">
															<?php if ($showRentPill): ?>
															<div class="essential-kit-pill essential-kit-pill--rent">
																Rent Locally
																<div class="essential-kit-pill-info" title="<?php echo htmlspecialchars($rentPopoverText); ?>">
																	<svg xmlns="http://www.w3.org/2000/svg" height="12px" viewBox="0 0 24 24" width="12px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"></path><path d="M0 0h24v24H0V0z" opacity=".87"></path></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"></path></svg>
																</div>
															</div>
															<?php endif; ?>
															<?php if ($showFreePill): ?>
															<div class="essential-kit-pill essential-kit-pill--free">
																FREE Hire
																<div class="essential-kit-pill-info" title="<?php echo htmlspecialchars($freePopoverText); ?>">
																	<svg xmlns="http://www.w3.org/2000/svg" height="12px" viewBox="0 0 24 24" width="12px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"></path><path d="M0 0h24v24H0V0z" opacity=".87"></path></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"></path></svg>
																</div>
															</div>
															<?php endif; ?>
														</div>
														<?php endif; ?>
													</div>
												<?php endif; ?>
												<div class="essential-kit-content">
													<?php if (!empty($kit['item_name'])): ?>
														<h4 class="essential-kit-title"><?php echo htmlspecialchars($kit['item_name']); ?></h4>
													<?php endif; ?>
													<?php if (!empty($kit['item_description'])): ?>
														<p class="essential-kit-description"><?php echo $kit['item_description']; ?></p>
													<?php endif; ?>
													<?php if ($showShopButton): ?>
														<div class="essential-kit-shop-indicator">
															Shop now
															<img src="/templates/zenbase/icons/trips/open_in_new.svg" width="16" height="16" alt="">
														</div>
													<?php endif; ?>
												</div>
											<?php if ($showShopButton): ?>
											</a>
											<?php else: ?>
											</div>
											<?php endif; ?>
										<?php endforeach; ?>
									</div>
								<?php endif; ?>
							</div>
						</div>

						<!-- Duplicate header after grid with top margin -->
						<div class="d-flex justify-content-between align-items-center mb-4" style="margin-top: 25px;">
							<p class="intro mb-0">Explore our essential kit picks so you're comfortable, safe, and ready for every step. <a style="color:#FE7720" data-bs-toggle="modal" data-bs-target="#packingListModal">Download</a> the full checklist to nail your packing. </p>

							<div class="essential-kit-header-button">
								<?php
								$packingList = ZenModelHelper::getCopyItemsByAlias('com_zenholidays', $id, 'packing-list');
								if ($packingList && isset($packingList['packing-list']) && isset($packingList['packing-list']->items)):
									$content = preg_replace('/^<p[^>]*>(.*)<\/p>$/s', '$1', $packingList['packing-list']->items[0]->content);
									$formData = json_decode($content, true);
									if ($formData && isset($formData['form_url'], $formData['iframe_id'], $formData['form_id'], $formData['form_name'], $formData['form_title'])):
								?>
								<button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#packingListModal" style="white-space: nowrap;">
									Download full packing list
									<svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" class="ms-2">
										<path d="M9.00002 7.21051C8.61002 7.60051 8.61002 8.23051 9.00002 8.62051L12.88 12.5005L9.00002 16.3805C8.61002 16.7705 8.61002 17.4005 9.00002 17.7905C9.39002 18.1805 10.02 18.1805 10.41 17.7905L15 13.2005C15.39 12.8105 15.39 12.1805 15 11.7905L10.41 7.20051C10.03 6.82051 9.39002 6.82051 9.00002 7.21051Z" fill="currentColor"/>
									</svg>
								</button>
								<?php
									endif;
								endif;
								?>
							</div>
						</div>

					<?php endforeach; ?>
				<?php else: ?>
					<div class="zen-alert zen-alert--info">
						<p>Essential kit information is not available for this trip.</p>
					</div>
				<?php endif; ?>
			</div>
		</div>
	</div>
</div>

<!-- Load the unified Essential Kit manager -->
<script src="/templates/zenbase/js/essential-kit-manager.js"></script>
