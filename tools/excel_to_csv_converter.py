#!/usr/bin/env python3
"""
Excel to CSV Converter for Essential Kit Data

This script converts the Essential Kit Excel file with multiple tabs
to separate CSV files for easier processing.
"""

import os
import sys
import csv
from pathlib import Path
from openpyxl import load_workbook

def convert_excel_to_csv(excel_path, output_dir):
    """
    Convert Excel file with multiple sheets to separate CSV files

    Args:
        excel_path (str): Path to the Excel file
        output_dir (str): Directory to save CSV files
    """

    # Create output directory if it doesn't exist
    output_path = Path(output_dir)
    output_path.mkdir(parents=True, exist_ok=True)

    try:
        # Load the workbook
        workbook = load_workbook(excel_path, read_only=True)

        print(f"Found {len(workbook.sheetnames)} sheets in Excel file:")
        for sheet_name in workbook.sheetnames:
            print(f"  - {sheet_name}")

        # Convert each sheet to CSV
        for sheet_name in workbook.sheetnames:
            # Get the worksheet
            worksheet = workbook[sheet_name]

            # Clean sheet name for filename (remove special characters)
            clean_sheet_name = "".join(c for c in sheet_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            clean_sheet_name = clean_sheet_name.replace(' ', '_').lower()

            # Create CSV filename
            csv_filename = f"{clean_sheet_name}.csv"
            csv_path = output_path / csv_filename

            # Convert worksheet to CSV
            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                csv_writer = csv.writer(csvfile)

                rows_written = 0
                columns_count = 0
                first_row_data = []

                for row_num, row in enumerate(worksheet.iter_rows(values_only=True), 1):
                    # Skip completely empty rows
                    if all(cell is None or str(cell).strip() == '' for cell in row):
                        continue

                    # Convert None values to empty strings
                    row_data = [str(cell) if cell is not None else '' for cell in row]
                    csv_writer.writerow(row_data)

                    if rows_written == 0:
                        columns_count = len(row_data)
                        first_row_data = row_data

                    rows_written += 1

            print(f"Converted '{sheet_name}' -> {csv_filename} ({rows_written} rows, {columns_count} columns)")

            # Show first row for verification
            if first_row_data:
                print(f"  First row: {first_row_data}")
                print()

        workbook.close()
        print(f"All sheets converted successfully to: {output_dir}")
        return True

    except Exception as e:
        print(f"Error converting Excel file: {e}")
        return False

def main():
    # Set paths
    script_dir = Path(__file__).parent
    excel_path = script_dir.parent / "docs" / "Essential Kit free hire or rent.xlsx"
    output_dir = script_dir.parent / "csv_data"
    
    print("Essential Kit Excel to CSV Converter")
    print("=" * 40)
    print(f"Excel file: {excel_path}")
    print(f"Output directory: {output_dir}")
    print()
    
    # Check if Excel file exists
    if not excel_path.exists():
        print(f"Error: Excel file not found at {excel_path}")
        sys.exit(1)
    
    # Convert Excel to CSV
    success = convert_excel_to_csv(excel_path, output_dir)
    
    if success:
        print("Conversion completed successfully!")
        print(f"CSV files are available in: {output_dir}")
    else:
        print("Conversion failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
