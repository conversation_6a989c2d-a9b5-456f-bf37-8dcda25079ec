#!/usr/bin/env python3
"""
Essential Kit Transformation Summary

This script generates a summary report of the transformation process,
showing what changes were made and providing validation of the results.
"""

import json
import csv
from pathlib import Path
from typing import Dict, List, Any

def analyze_transformation_results():
    """Analyze and report on the transformation results"""
    
    script_dir = Path(__file__).parent
    original_dir = script_dir.parent / "docs" / "updated"
    transformed_dir = script_dir.parent / "transformed_json"
    csv_dir = script_dir.parent / "csv_data"
    
    print("Essential Kit Transformation Summary Report")
    print("=" * 60)
    print()
    
    # Count files
    original_files = list(original_dir.glob("*.json"))
    transformed_files = list(transformed_dir.glob("transformed_*.json"))
    csv_files = list(csv_dir.glob("*.csv"))
    
    print(f"📁 Files processed:")
    print(f"   Original JSON files: {len(original_files)}")
    print(f"   Transformed JSON files: {len(transformed_files)}")
    print(f"   CSV data files: {len(csv_files)}")
    print()
    
    # Analyze transformations
    badge_additions = 0
    popover_additions = 0
    free_equipment_hire_transformations = 0
    
    for transformed_file in transformed_files:
        # Find corresponding original file
        original_name = transformed_file.name.replace("transformed_", "")
        original_file = original_dir / original_name
        
        if not original_file.exists():
            continue
            
        # Load both files
        with open(original_file, 'r', encoding='utf-8') as f:
            original_data = json.load(f)
        
        with open(transformed_file, 'r', encoding='utf-8') as f:
            transformed_data = json.load(f)
        
        # Count badge additions
        for i, product in enumerate(transformed_data.get('products', [])):
            original_product = original_data.get('products', [{}])[i] if i < len(original_data.get('products', [])) else {}
            
            # Check for new badges
            if product.get('free_hire') and not original_product.get('free_hire'):
                badge_additions += 1
            if product.get('rent_locally') and not original_product.get('rent_locally'):
                badge_additions += 1
            
            # Check for new popovers
            if product.get('free_hire_popover') and not original_product.get('free_hire_popover'):
                popover_additions += 1
            if product.get('rent_locally_popover') and not original_product.get('rent_locally_popover'):
                popover_additions += 1
        
        # Check free equipment hire transformation
        original_feh = original_data.get('free_equipment_hire')
        transformed_feh = transformed_data.get('free_equipment_hire')
        
        if isinstance(original_feh, (bool, str)) and isinstance(transformed_feh, dict):
            free_equipment_hire_transformations += 1
    
    print(f"🔄 Transformation statistics:")
    print(f"   New badges added: {badge_additions}")
    print(f"   New popovers added: {popover_additions}")
    print(f"   Free equipment hire objects created: {free_equipment_hire_transformations}")
    print()
    
    # Show sample transformations
    print("📋 Sample transformations:")
    print()
    
    # Pick the first transformed file for detailed analysis
    if transformed_files:
        sample_file = transformed_files[0]
        original_name = sample_file.name.replace("transformed_", "")
        original_file = original_dir / original_name
        
        if original_file.exists():
            with open(original_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)
            
            with open(sample_file, 'r', encoding='utf-8') as f:
                transformed_data = json.load(f)
            
            print(f"   File: {original_name}")
            print(f"   Products: {len(transformed_data.get('products', []))}")
            
            # Show free equipment hire transformation
            original_feh = original_data.get('free_equipment_hire')
            transformed_feh = transformed_data.get('free_equipment_hire')
            
            print(f"   Free equipment hire:")
            print(f"     Before: {type(original_feh).__name__} - {original_feh}")
            print(f"     After:  {type(transformed_feh).__name__} with {len(transformed_feh)} properties")
            
            # Show badge examples
            products_with_badges = [p for p in transformed_data.get('products', []) 
                                  if p.get('free_hire') or p.get('rent_locally')]
            
            if products_with_badges:
                print(f"   Products with badges: {len(products_with_badges)}")
                for product in products_with_badges[:3]:  # Show first 3
                    badges = []
                    if product.get('free_hire'):
                        badges.append("FREE Hire")
                    if product.get('rent_locally'):
                        badges.append("Rent Locally")
                    print(f"     - {product['item_name']}: {', '.join(badges)}")
                    
                    # Show popover examples
                    if product.get('free_hire_popover'):
                        print(f"       Free hire popover: \"{product['free_hire_popover'][:50]}...\"")
                    if product.get('rent_locally_popover'):
                        print(f"       Rent locally popover: \"{product['rent_locally_popover'][:50]}...\"")
            print()
    
    # CSV data summary
    print("📊 CSV data summary:")
    total_items = 0
    items_with_free_hire = 0
    items_with_rent_locally = 0
    
    for csv_file in csv_files:
        if csv_file.stem == 'copy_of_copy_of_copy_of_blank':
            continue
            
        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if not row.get('Essential kit item', '').strip():
                        continue
                    
                    total_items += 1
                    
                    if row.get('Free hire', '').strip().lower() == 'yes':
                        items_with_free_hire += 1
                    
                    if row.get('Rent Locally', '').strip().lower() == 'yes':
                        items_with_rent_locally += 1
        except Exception as e:
            print(f"   Error reading {csv_file}: {e}")
    
    print(f"   Total items across all CSV files: {total_items}")
    print(f"   Items with 'Free hire': {items_with_free_hire}")
    print(f"   Items with 'Rent Locally': {items_with_rent_locally}")
    print()
    
    # Validation
    print("✅ Validation:")
    all_valid = True
    
    for transformed_file in transformed_files:
        try:
            with open(transformed_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check required structure
            if 'products' not in data:
                print(f"   ❌ Missing 'products' in {transformed_file.name}")
                all_valid = False
            
            if 'free_equipment_hire' not in data:
                print(f"   ❌ Missing 'free_equipment_hire' in {transformed_file.name}")
                all_valid = False
            
            # Check free_equipment_hire is object format
            feh = data.get('free_equipment_hire')
            if not isinstance(feh, dict):
                print(f"   ❌ free_equipment_hire is not object format in {transformed_file.name}")
                all_valid = False
            
            # Check products structure
            for i, product in enumerate(data.get('products', [])):
                required_fields = ['item_name', 'product_name', 'item_description', 'image']
                for field in required_fields:
                    if not product.get(field):
                        print(f"   ❌ Missing {field} in product {i} of {transformed_file.name}")
                        all_valid = False
                        
        except json.JSONDecodeError as e:
            print(f"   ❌ Invalid JSON in {transformed_file.name}: {e}")
            all_valid = False
        except Exception as e:
            print(f"   ❌ Error validating {transformed_file.name}: {e}")
            all_valid = False
    
    if all_valid:
        print("   ✅ All transformed files are valid!")
    
    print()
    print("🎉 Transformation completed successfully!")
    print(f"   Transformed files are available in: {transformed_dir}")
    print("   Files are ready to be used with the new Essential Kit format.")

if __name__ == "__main__":
    analyze_transformation_results()
