#!/usr/bin/env python3
"""
Essential Kit JSON Transformer

This script transforms essential kit JSON files from the existing format to the new format
described in the documentation. It uses the CSV data extracted from the Excel file to
populate badge and popover information.
"""

import os
import sys
import json
import csv
import re
from pathlib import Path
from typing import Dict, List, Optional, Any

class EssentialKitTransformer:
    def __init__(self, csv_dir: str, json_dir: str, output_dir: str):
        self.csv_dir = Path(csv_dir)
        self.json_dir = Path(json_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load CSV data for badge/popover information
        self.csv_data = self._load_csv_data()
        
        # Mapping from CSV sheet names to holiday aliases
        self.sheet_to_holiday_mapping = {
            'ebc': ['everest-base-camp-trek'],
            'ebc_gokyo': ['everest-base-camp-via-gokyo-valley'],
            'ebc_3_passes': ['everest-three-passes-trek'],
            'annapurna_bc': ['annapurna-base-camp-trek'],
            'annapurna_c': ['annapurna-circuit-trek'],
            'langtang': ['langtang-valley-trek'],
            'island_peak': ['island-peak-expedition'],
            'ult_island_peak': ['ultimate-island-peak-and-everest-base-camp-expedition'],
            'ult_mera_peak': ['ultimate-mera-peak-expedition'],
            'mp_tomacaya': ['machu-picchu-tomacaya'],
            'mp_inca': ['inca-trail-to-machu-picchu'],
            'kili': ['kilimanjaro-machame-route', 'kilimanjaro-lemosho-route'],
            'toubkal_weekender': ['mt-toubkal-roof-of-the-north-weekender'],
            'toubkal_8_day': ['mt-toubkal-8-day-adventure'],
            'tmb': ['tour-du-mont-blanc'],
            'mt_olympus': ['mt-olympus-myths-and-legends-weekender']
        }
    
    def _load_csv_data(self) -> Dict[str, List[Dict[str, str]]]:
        """Load all CSV files and return structured data"""
        csv_data = {}
        
        for csv_file in self.csv_dir.glob('*.csv'):
            sheet_name = csv_file.stem
            if sheet_name == 'copy_of_copy_of_copy_of_blank':
                continue  # Skip blank template
                
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = []
                    for row in reader:
                        # Skip empty rows
                        if not row.get('Essential kit item', '').strip():
                            continue
                        rows.append(row)
                    csv_data[sheet_name] = rows
                    print(f"Loaded {len(rows)} items from {sheet_name}.csv")
            except Exception as e:
                print(f"Error loading {csv_file}: {e}")
        
        return csv_data
    
    def _normalize_item_name(self, name: str) -> str:
        """Normalize item names for matching between CSV and JSON"""
        if not name:
            return ""
        
        # Convert to lowercase and remove extra spaces
        normalized = re.sub(r'\s+', ' ', name.lower().strip())
        
        # Handle common variations
        variations = {
            'water proof jacket': 'waterproof jacket',
            'base layer': 'base layer top',
            'technical t-shirt': 'technical t shirt',
            'hydration reservoir': 'hydration system',
            'fleece mid layer': 'fleece midlayer',
        }
        
        return variations.get(normalized, normalized)
    
    def _find_csv_data_for_item(self, item_name: str, sheet_name: str) -> Optional[Dict[str, str]]:
        """Find CSV data for a specific item"""
        if sheet_name not in self.csv_data:
            return None
        
        normalized_item = self._normalize_item_name(item_name)
        
        for row in self.csv_data[sheet_name]:
            csv_item = self._normalize_item_name(row.get('Essential kit item', ''))
            if csv_item == normalized_item:
                return row
        
        return None
    
    def _get_sheet_name_for_holiday(self, holiday_alias: str) -> Optional[str]:
        """Get the CSV sheet name for a holiday alias"""
        for sheet_name, holiday_aliases in self.sheet_to_holiday_mapping.items():
            if holiday_alias in holiday_aliases:
                return sheet_name
        return None
    
    def _transform_product(self, product: Dict[str, Any], sheet_name: str) -> Dict[str, Any]:
        """Transform a single product to the new format"""
        transformed = {
            'item_name': product.get('item_name', ''),
            'product_name': product.get('product_name', ''),
            'item_description': product.get('item_description', ''),
            'image': product.get('image', '')
        }
        
        # Add URL if present
        if product.get('url'):
            transformed['url'] = product['url']
        
        # Find corresponding CSV data for badge information
        csv_row = self._find_csv_data_for_item(product.get('item_name', ''), sheet_name)
        
        if csv_row:
            # Check for free hire
            if csv_row.get('Free hire', '').strip().lower() == 'yes':
                transformed['free_hire'] = True
                # Add custom popover message if available
                message = csv_row.get('Message', '').strip()
                if message:
                    transformed['free_hire_popover'] = message
            
            # Check for rent locally
            if csv_row.get('Rent Locally', '').strip().lower() == 'yes':
                transformed['rent_locally'] = True
                # Add custom popover message if available
                message = csv_row.get('Message', '').strip()
                if message:
                    transformed['rent_locally_popover'] = message
        else:
            # Fallback to existing badge data if no CSV match
            if product.get('free_hire'):
                transformed['free_hire'] = product['free_hire']
            if product.get('rent_locally'):
                transformed['rent_locally'] = product['rent_locally']
            if product.get('free_hire_popover'):
                transformed['free_hire_popover'] = product['free_hire_popover']
            if product.get('rent_locally_popover'):
                transformed['rent_locally_popover'] = product['rent_locally_popover']
        
        return transformed
    
    def _transform_free_equipment_hire(self, original_data: Any) -> Dict[str, Any]:
        """Transform free_equipment_hire to new object format"""
        # If it's already an object, return as-is (already in new format)
        if isinstance(original_data, dict):
            return original_data
        
        # If it's a boolean, convert to new format
        if isinstance(original_data, bool):
            if original_data:
                return {
                    "show": True,
                    "title": "FREE equipment hire!",
                    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
                    "show_footer": True,
                    "footer_text": "Check out our partner discounts for extra kit.",
                    "url": "https://evertrek.com/partner-discounts"
                }
            else:
                return {"show": False}
        
        # If it's a string "true"/"false", convert accordingly
        if isinstance(original_data, str):
            if original_data.lower() == 'true':
                return {
                    "show": True,
                    "title": "FREE equipment hire!",
                    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
                    "show_footer": True,
                    "footer_text": "Check out our partner discounts for extra kit.",
                    "url": "https://evertrek.com/partner-discounts"
                }
            else:
                return {"show": False}
        
        # Default fallback
        return {"show": True}

    def transform_json_file(self, json_file: Path) -> bool:
        """Transform a single JSON file to the new format"""
        try:
            # Extract holiday alias from filename
            filename = json_file.stem
            # Pattern: essential-kit_YYYY-MM-DD_HH-MM-SS_holiday-alias
            parts = filename.split('_')
            if len(parts) >= 4:
                holiday_alias = '_'.join(parts[3:])
            else:
                print(f"Warning: Could not extract holiday alias from {filename}")
                return False

            # Find corresponding CSV sheet
            sheet_name = self._get_sheet_name_for_holiday(holiday_alias)
            if not sheet_name:
                print(f"Warning: No CSV mapping found for holiday '{holiday_alias}'")
                sheet_name = 'ebc'  # Default fallback

            # Load existing JSON
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Transform the data
            transformed_data = {
                "products": [],
                "free_equipment_hire": self._transform_free_equipment_hire(
                    data.get('free_equipment_hire', True)
                )
            }

            # Transform each product
            for product in data.get('products', []):
                transformed_product = self._transform_product(product, sheet_name)
                transformed_data['products'].append(transformed_product)

            # Generate output filename
            output_filename = f"transformed_{filename}.json"
            output_path = self.output_dir / output_filename

            # Save transformed JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(transformed_data, f, indent=2, ensure_ascii=False)

            print(f"Transformed: {json_file.name} -> {output_filename}")
            print(f"  Holiday: {holiday_alias}")
            print(f"  CSV sheet: {sheet_name}")
            print(f"  Products: {len(transformed_data['products'])}")
            print()

            return True

        except Exception as e:
            print(f"Error transforming {json_file}: {e}")
            return False

    def transform_all_files(self) -> None:
        """Transform all JSON files in the input directory"""
        json_files = list(self.json_dir.glob('essential-kit_*.json'))

        if not json_files:
            print("No essential kit JSON files found to transform.")
            return

        print(f"Found {len(json_files)} JSON files to transform")
        print("=" * 50)

        success_count = 0
        for json_file in json_files:
            if self.transform_json_file(json_file):
                success_count += 1

        print("=" * 50)
        print("Transformation completed!")
        print(f"Successfully transformed: {success_count}/{len(json_files)} files")
        print(f"Output directory: {self.output_dir}")

def main():
    # Set paths
    script_dir = Path(__file__).parent
    csv_dir = script_dir.parent / "csv_data"
    json_dir = script_dir.parent / "json"
    output_dir = script_dir.parent / "transformed_json"

    print("Essential Kit JSON Transformer")
    print("=" * 40)
    print(f"CSV data directory: {csv_dir}")
    print(f"JSON input directory: {json_dir}")
    print(f"Output directory: {output_dir}")
    print()

    # Check if directories exist
    if not csv_dir.exists():
        print(f"Error: CSV directory not found at {csv_dir}")
        print("Please run the Excel to CSV converter first.")
        sys.exit(1)

    if not json_dir.exists():
        print(f"Error: JSON directory not found at {json_dir}")
        sys.exit(1)

    # Create transformer and run
    transformer = EssentialKitTransformer(csv_dir, json_dir, output_dir)
    transformer.transform_all_files()

if __name__ == "__main__":
    main()
