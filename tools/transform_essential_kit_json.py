#!/usr/bin/env python3
"""
Essential Kit JSON Transformer

This script transforms essential kit JSON files from the existing format to the new format
described in the documentation. It uses the CSV data extracted from the Excel file to
populate badge and popover information.
"""

import os
import sys
import json
import csv
import re
from pathlib import Path
from typing import Dict, List, Optional, Any

class EssentialKitTransformer:
    def __init__(self, csv_dir: str, json_dir: str, output_dir: str):
        self.csv_dir = Path(csv_dir)
        self.json_dir = Path(json_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Load CSV data for badge/popover information
        self.csv_data = self._load_csv_data()
        
        # Mapping from JSON filenames to CSV sheet names
        self.json_to_csv_mapping = {
            'ebc.json': 'ebc',
            'ebcgv.json': 'ebc_gokyo',
            'eve3.json': 'ebc_3_passes',
            'abc.json': 'annapurna_bc',
            'ann.json': 'annapurna_c',
            'lvt.json': 'langtang',
            'isl.json': 'island_peak',
            'uipeb.json': 'ult_island_peak',
            'mer.json': 'ult_mera_peak',
            'mac.json': 'mp_tomacaya',
            'mpinca.json': 'mp_inca',
            'kmj.json': 'kili',
            'toubnw.json': 'toubkal_weekender',
            'toubnw8.json': 'toubkal_8_day',
            'tmb.json': 'tmb',
            'molympus.json': 'mt_olympus',
            # New mappings - these don't have corresponding CSV files
            'btw.json': None,    # brecon training weekend - no CSV
            'k2bc.json': None,   # k2 basecamp - no CSV
            'nwtw.json': None,   # snowdonia - no CSV
            'sco.json': None     # scotland - no CSV
        }
    
    def _load_csv_data(self) -> Dict[str, List[Dict[str, str]]]:
        """Load all CSV files and return structured data"""
        csv_data = {}
        
        for csv_file in self.csv_dir.glob('*.csv'):
            sheet_name = csv_file.stem
            if sheet_name == 'copy_of_copy_of_copy_of_blank':
                continue  # Skip blank template
                
            try:
                with open(csv_file, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    rows = []
                    for row in reader:
                        # Skip empty rows
                        if not row.get('Essential kit item', '').strip():
                            continue
                        rows.append(row)
                    csv_data[sheet_name] = rows
                    print(f"Loaded {len(rows)} items from {sheet_name}.csv")
            except Exception as e:
                print(f"Error loading {csv_file}: {e}")
        
        return csv_data
    
    def _normalize_item_name(self, name: str) -> str:
        """Normalize item names for matching between CSV and JSON"""
        if not name:
            return ""
        
        # Convert to lowercase and remove extra spaces
        normalized = re.sub(r'\s+', ' ', name.lower().strip())
        
        # Handle common variations
        variations = {
            'water proof jacket': 'waterproof jacket',
            'base layer': 'base layer top',
            'technical t-shirt': 'technical t shirt',
            'hydration reservoir': 'hydration system',
            'fleece mid layer': 'fleece midlayer',
        }
        
        return variations.get(normalized, normalized)
    
    def _find_csv_data_for_item(self, item_name: str, sheet_name: str) -> Optional[Dict[str, str]]:
        """Find CSV data for a specific item"""
        if sheet_name not in self.csv_data:
            return None
        
        normalized_item = self._normalize_item_name(item_name)
        
        for row in self.csv_data[sheet_name]:
            csv_item = self._normalize_item_name(row.get('Essential kit item', ''))
            if csv_item == normalized_item:
                return row
        
        return None
    
    def _get_sheet_name_for_json_file(self, json_filename: str) -> Optional[str]:
        """Get the CSV sheet name for a JSON filename"""
        return self.json_to_csv_mapping.get(json_filename, None)  # Return None if no mapping found
    
    def _transform_product(self, product: Dict[str, Any], sheet_name: str) -> Dict[str, Any]:
        """Transform a single product to the new format"""
        transformed = {
            'item_name': product.get('item_name', ''),
            'product_name': product.get('product_name', ''),
            'item_description': product.get('item_description', ''),
            'image': product.get('image', '')
        }

        # Remove URLs - don't add URL field
        
        # Find corresponding CSV data for badge information
        # If sheet_name is None, don't add any badges
        if sheet_name is not None:
            csv_row = self._find_csv_data_for_item(product.get('item_name', ''), sheet_name)

            if csv_row:
                # Check for free hire
                if csv_row.get('Free hire', '').strip().lower() == 'yes':
                    transformed['free_hire'] = True
                    # Add custom popover message if available
                    message = csv_row.get('Message', '').strip()
                    if message:
                        transformed['free_hire_popover'] = message

                # Check for rent locally
                if csv_row.get('Rent Locally', '').strip().lower() == 'yes':
                    transformed['rent_locally'] = True
                    # Add custom popover message if available
                    message = csv_row.get('Message', '').strip()
                    if message:
                        transformed['rent_locally_popover'] = message
            else:
                # Fallback to existing badge data if no CSV match but sheet exists
                if product.get('free_hire'):
                    transformed['free_hire'] = product['free_hire']
                if product.get('rent_locally'):
                    transformed['rent_locally'] = product['rent_locally']
                if product.get('free_hire_popover'):
                    transformed['free_hire_popover'] = product['free_hire_popover']
                if product.get('rent_locally_popover'):
                    transformed['rent_locally_popover'] = product['rent_locally_popover']
        # If sheet_name is None, don't add any badges (no CSV data available)
        
        return transformed
    
    def _transform_free_equipment_hire(self, original_data: Any) -> Dict[str, Any]:
        """Transform free_equipment_hire to new object format"""
        # If it's already an object, modify to hide footer
        if isinstance(original_data, dict):
            # Copy existing object but hide footer
            result = original_data.copy()
            result["show_footer"] = False
            # Remove URL if present
            if "url" in result:
                del result["url"]
            return result

        # If it's a boolean, convert to new format
        if isinstance(original_data, bool):
            if original_data:
                return {
                    "show": True,
                    "title": "FREE equipment hire!",
                    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
                    "show_footer": False
                }
            else:
                return {"show": False}

        # If it's a string "true"/"false", convert accordingly
        if isinstance(original_data, str):
            if original_data.lower() == 'true':
                return {
                    "show": True,
                    "title": "FREE equipment hire!",
                    "content": "&lt;p&gt;All EverTrekkers receive &lt;span class='free-equipment-hire-highlight'&gt;FREE winter sleeping bags&lt;/span&gt; and a &lt;span class='free-equipment-hire-highlight'&gt;FREE £110 down jacket&lt;/span&gt; – just let us know when booking to reserve.&lt;/p&gt;&lt;p&gt;&lt;strong&gt;PLUS:&lt;/strong&gt; Get a &lt;span class='free-equipment-hire-highlight'&gt;FREE 80 Ltr duffel bag, t-shirt, cap, and map&lt;/span&gt; when you arrive in Kathmandu!&lt;/p&gt;",
                    "show_footer": False
                }
            else:
                return {"show": False}

        # Default fallback
        return {"show": True, "show_footer": False}

    def transform_json_file(self, json_file: Path) -> bool:
        """Transform a single JSON file to the new format"""
        try:
            # Get filename for mapping
            filename = json_file.name

            # Find corresponding CSV sheet using filename mapping
            sheet_name = self._get_sheet_name_for_json_file(filename)

            # Load existing JSON
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Transform the data
            transformed_data = {
                "products": [],
                "free_equipment_hire": self._transform_free_equipment_hire(
                    data.get('free_equipment_hire', True)
                )
            }

            # Transform each product
            for product in data.get('products', []):
                transformed_product = self._transform_product(product, sheet_name)
                transformed_data['products'].append(transformed_product)

            # Generate output filename
            output_filename = f"transformed_{filename}"
            output_path = self.output_dir / output_filename

            # Save transformed JSON
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(transformed_data, f, indent=2, ensure_ascii=False)

            print(f"✅ {json_file.name} -> {output_filename} ({len(transformed_data['products'])} products)")

            return True

        except Exception as e:
            print(f"Error transforming {json_file}: {e}")
            return False

    def transform_all_files(self) -> None:
        """Transform all JSON files in the input directory"""
        json_files = list(self.json_dir.glob('*.json'))

        if not json_files:
            print("No essential kit JSON files found to transform.")
            return

        print(f"Found {len(json_files)} JSON files to transform")
        print()
        print("📋 Filename to CSV Sheet Mapping:")
        print("-" * 40)
        for json_file in sorted(json_files):
            sheet_name = self._get_sheet_name_for_json_file(json_file.name)
            if sheet_name is None:
                print(f"  {json_file.name:<15} -> {'NO CSV MAPPING':<20} (no badges)")
            else:
                csv_items = len(self.csv_data.get(sheet_name, []))
                print(f"  {json_file.name:<15} -> {sheet_name:<20} ({csv_items} CSV items)")
        print()
        print("=" * 50)

        success_count = 0
        for json_file in json_files:
            if self.transform_json_file(json_file):
                success_count += 1

        print("=" * 50)
        print("Transformation completed!")
        print(f"Successfully transformed: {success_count}/{len(json_files)} files")
        print()

        # Show mapping summary
        print("📊 Mapping Summary:")
        mapping_stats = {}
        for json_file in json_files:
            sheet_name = self._get_sheet_name_for_json_file(json_file.name)
            if sheet_name not in mapping_stats:
                mapping_stats[sheet_name] = []
            mapping_stats[sheet_name].append(json_file.name)

        for sheet_name, json_files_list in sorted(mapping_stats.items(), key=lambda x: (x[0] is None, x[0])):
            if sheet_name is None:
                print(f"  NO CSV MAPPING (no badges) <- {len(json_files_list)} JSON files:")
                for json_file in sorted(json_files_list):
                    print(f"    - {json_file}")
            else:
                csv_items = len(self.csv_data.get(sheet_name, []))
                print(f"  {sheet_name} ({csv_items} CSV items) <- {len(json_files_list)} JSON files:")
                for json_file in sorted(json_files_list):
                    print(f"    - {json_file}")

        print()
        print(f"Output directory: {self.output_dir}")

def main():
    # Set paths
    script_dir = Path(__file__).parent
    csv_dir = script_dir.parent / "csv_data"
    json_dir = script_dir.parent / "docs" / "updated"
    output_dir = script_dir.parent / "transformed_json"

    print("Essential Kit JSON Transformer")
    print("=" * 40)
    print(f"CSV data directory: {csv_dir}")
    print(f"JSON input directory: {json_dir}")
    print(f"Output directory: {output_dir}")
    print()

    # Check if directories exist
    if not csv_dir.exists():
        print(f"Error: CSV directory not found at {csv_dir}")
        print("Please run the Excel to CSV converter first.")
        sys.exit(1)

    if not json_dir.exists():
        print(f"Error: JSON directory not found at {json_dir}")
        sys.exit(1)

    # Create transformer and run
    transformer = EssentialKitTransformer(csv_dir, json_dir, output_dir)
    transformer.transform_all_files()

if __name__ == "__main__":
    main()
